import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getQuestions } from "../hooks/getQuestions";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { postQuestion } from "../hooks/postQuestions";
import "../../../Components/Loading/Loading2.css";
import styles from "./styling.module.css";
import { updateStatus } from "../hooks/updateStatus";
import * as moment from "moment";
import https from "../../../https";
import worker from "workerize-loader!./worker"; // eslint-disable-line import/no-webpack-loader-syntax
import { BsFillClockFill } from "react-icons/bs";
import { AiOutlineArrowLeft, AiOutlineArrowRight } from "react-icons/ai";
import "../../../Components/Loading/Loading4.css";
import { FaRunning } from "react-icons/fa";
import { updateQuestion } from "../hooks/updateQuestion";
import { useDispatch, useSelector } from "react-redux";
import { setExamtoTrue } from "../../../redux/reducers/ExamDone/ExamDoneSlice";
import { postPictureData } from "../hooks/postPictureData";
import { GoArrowRight } from "react-icons/go";
import closeImg from "../../../Dexta_assets/closeModal.png";
import { setQuestionsTotal } from "../../../redux/reducers/QuestionsTotal/QuestionsSlice";
import { setCurrentQuestion } from "../../../redux/reducers/CurrentQuestion/CurrentQuestionSlice";
import closeIcon from "../../../Dexta_assets/closeModal.png";
import eye from "../../../Dexta_assets/magnifier.png";
import { toast, ToastContainer, Zoom } from "react-toastify";
import { setResumeToFalse } from "../../../redux/reducers/ResumeTest/ResumeSlice";
import { setQuestionNumberResume } from "../../../redux/reducers/ResumedQuestion/ResumeQuestionNumberSlice";
import useWindowSize from "../../../Helpers/useWindowSize";
import { stopTest } from "../hooks/stopTest";
import { transformDescription } from "../../../Helpers/ParseTable";
import ExcelSheets from "../../../Components/Excel/Excelmain";
import { GiExpand } from "react-icons/gi";
import { useTranslation } from "react-i18next";

const Test = () => {
  let questionID = null;
  const size = useWindowSize();
  const isMobile = size.width <= 1336;
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const currentSection = JSON?.parse(localStorage.getItem("Current Module"));
  const timecheck = JSON?.parse(localStorage.getItem("time"));
  const [selectedOption, setSelectedOption] = useState([]);
  const [remaining, setRemaining] = useState(timecheck.seconds);
  const moduleData = JSON?.parse(localStorage.getItem("module"));
  const question = JSON?.parse(localStorage.getItem("questions"));
  const currentTimeUnix = moment().unix();
  const minsec = JSON?.parse(localStorage.getItem("minsec"));
  const [response, setResponse] = useState(null);
  const [excelApidata, SetExcelApiData] = useState(null);
  const [accessDenied, setAccessDenied] = useState(false);
  const [camLogs, setCamLogs] = useState(null);
  const [isOutside, setIsOutside] = useState(false);
  const theme = JSON?.parse(localStorage.getItem("theme"));
  const getQuestion = JSON.parse(localStorage.getItem("prevQuestion")) || [];
  const [questState, setQuestState] = useState(false);
  const [fetchingOptions, setFetchingOptions] = useState(false);
  const [res_id, setResID] = useState(0);
  const [skipModal, setSkipModal] = useState(false);
  const dispatch = useDispatch();
  const [counter, setCounter] = useState(0);
  const [screenshotInterval, setScreenshotInterval] = useState(null);
  const [backLoading, setBackLoading] = useState(false);
  const practiceSection = JSON?.parse(localStorage.getItem("practice_section"));
  const questionsTotal = useSelector((state) => state.questionsTotal.setTotal);
  const [pictureLink, setPictureLink] = useState(null);
  const [totalDuration, setTotalDuration] = useState(minsec.secleft);
  const completion_check = localStorage.getItem("test_completed");
  const user_exists = localStorage.getItem("user_exists");
  const resumeTest = useSelector((state) => state.resumeExam.setResume);
  const [cellsFilled, SetCellsFilled] = useState(false);
  const [showSheetModal, setShowSheetModal] = useState(false);
  const language = localStorage.getItem("i18nextLng");
  const resumeTestQuestion = useSelector(
    (state) => state.resumeQuestionNumber.setResumeQuestion
  );
  const [isFullscreen, setIsFullscreen] = useState(
    Boolean(document.fullscreenElement)
  );
  const { t } = useTranslation();

  console.log(cellsFilled, "hello cells filled check");
  //#region useffect #1 -> starting exam, updating state in localstorage
  useEffect(() => {
    localStorage.setItem("exam", "start");
  }, []);
  //#endregion

  //#region useffect #2 -> Updating current question in localstorage
  useEffect(() => {
    if (data?.meta?.page) {
      dispatch(setCurrentQuestion(question?.current));
    }
  }, [question?.current]);
  //#endregion

  //#region useffect #3 -> Updating total questions cound in localstorage
  useEffect(() => {
    if (!questionsTotal) {
      dispatch(setQuestionsTotal(question?.total));
    }
  }, [question?.total, questionsTotal]);
  //#endregion

  //#region Fetching questions details
  const { data, error, isLoading } = useQuery(
    ["questions", currentSection, language],
    () => getQuestions(currentSection, language)
  );
  //#endregion

  //#region Handling back button
  const handleBackQuestion = () => {
    setBackLoading(true);
    localStorage.setItem("question_ID", 0);
    currentSection.pageQuestion = currentSection.pageQuestion - 1;
    localStorage.setItem("Current Module", JSON.stringify(currentSection));
    setTimeout(() => {
      setBackLoading(false);
    }, 1000);
  };
  //#endregion

  //#region fetching selected options
  useEffect(() => {
    const getSelectedQuestion = () => {
      if (!isLoading && data?.data[0]?.responseSubmitted?.id !== null) {
        setSelectedOption(data?.data[0]?.responseSubmitted?.selectedOptions);
        setResID(data?.data[0]?.responseSubmitted?.id);
        setQuestState(true);
      } else {
        setQuestState(false);
      }
    };
    getSelectedQuestion();
  }, [data?.data[0]?.responseSubmitted?.id, questState]);
  //#endregion

  //#region Handling Questions in localstorage
  const handleLocalStorage = (id) => {
    getQuestion.push(id); // Add the new questionID to the array
    localStorage.setItem("prevQuestion", JSON.stringify(getQuestion));
    localStorage.setItem("question_ID", 0);
  };
  //#endregion

  //#region handling workers
  const handleApiRequest = () => {
    let DataObj = {
      data: {
        usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
        evaluation: currentSection.evaluation,
        assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
        sectionId: parseInt(currentSection.ModuleID),
        questionId: parseInt(localStorage.getItem("Question")),
        proctoringSnapshot: pictureLink,
        webcam: camLogs !== null && camLogs,
        logType:
          data?.meta?.page === 1 && counter === 0 && totalDuration !== 0
            ? "Started"
            : data?.meta?.hasNextPage === false || totalDuration === 0
            ? "Ended"
            : null,
      },
      requestOptions: {
        authorization: `Bearer ${localStorage.getItem("CP-CANDIDATE-TOKEN")}`,
      },
    };
    const workerInstance = worker();
    workerInstance.postToApi({ data: DataObj });
    workerInstance.addEventListener("message", (e) => {
      const { type, result } = e.data;
      if (type === "POST_RESPONSE") {
        setResponse(result);
        workerInstance.terminate();
      }
    });
  };
  //#endregion

  //#region useffect #4 -> Monitoring mouse movement and sending mouse state to api
  useEffect(() => {
    const handleMouseOut = (event) => {
      if (
        !event.relatedTarget &&
        (event.clientY <= 0 ||
          event.clientX <= 0 ||
          event.clientX >= window.innerWidth ||
          event.clientY >= window.innerHeight)
      ) {
        if (!isOutside) {
          setIsOutside(true);
        }
      } else {
        if (isOutside) {
          setIsOutside(false);
        }
      }
    };

    window.addEventListener("mouseout", handleMouseOut);
    return () => {
      window.removeEventListener("mouseout", handleMouseOut);
    };
  }, [isOutside]);

  const handleMouseOutside = () => {
    let DataObj = {
      data: {
        usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
        evaluation: currentSection.evaluation,
        assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
        sectionId: parseInt(currentSection.ModuleID),
        questionId: parseInt(localStorage.getItem("Question")),
        mouseInAssessmentWindow: !isOutside,
      },
      requestOptions: {
        authorization: `Bearer ${localStorage.getItem("CP-CANDIDATE-TOKEN")}`,
      },
    };
    const workerInstance = worker();
    workerInstance.postToApi({ data: DataObj });
    workerInstance.addEventListener("message", (e) => {
      const { type, result } = e.data;
      if (type === "POST_RESPONSE") {
        setResponse(result);
        workerInstance.terminate();
      }
    });
  };

  useEffect(() => {
    handleMouseOutside();
  }, [isOutside]);
  //#endregion

  //#region After the cam take screenshot, image will be uploaded to s3
  const UploadImageToApi = async (blob) => {
    const formData = new FormData();
    formData.append("file", blob);
    formData.append("fileType", "user");
    try {
      const response = await https.post(
        `/evaluation_users_response_logs/assessment/${parseInt(
          localStorage.getItem("CANDIDATE-ASSESSMENT-ID")
        )}/upload-proctoring-snapshot`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      setPictureLink(response?.data?.link);
    } catch (error) {
      console.error(error);
    }
  };
  //#endregion

  //#region useffect #5 -> Sending logs to api
  useEffect(() => {
    if (data?.meta?.page === 1 || data?.meta?.hasNextPage === false) {
      handleApiRequest();
    } else if (totalDuration === 0) {
      handleApiRequest();
    } else {
      handleApiRequest();
    }
  }, [camLogs, accessDenied, data?.meta, counter, totalDuration]);
  //#endregion

  //#region Handle scroll
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [currentSection.pageQuestion]);
  //#endregion

  //#region useffect #6 and #7 -> Handle full screen
  const enterFullscreen = () => {
    const elementToFullscreen = document.documentElement;
    if (elementToFullscreen.requestFullscreen) {
      elementToFullscreen.requestFullscreen();
    } else if (elementToFullscreen.mozRequestFullScreen) {
      elementToFullscreen.mozRequestFullScreen();
    } else if (elementToFullscreen.webkitRequestFullscreen) {
      elementToFullscreen.webkitRequestFullscreen();
    } else if (elementToFullscreen.msRequestFullscreen) {
      elementToFullscreen.msRequestFullscreen();
    }
  };

  useEffect(() => {
    const onFullscreenChange = () => {
      const isCurrentlyFullscreen = Boolean(document.fullscreenElement);
      // Only update state if there's a change
      setIsFullscreen((prevState) => {
        if (prevState !== isCurrentlyFullscreen) {
          return isCurrentlyFullscreen;
        }
        return prevState;
      });
    };

    document.addEventListener("fullscreenchange", onFullscreenChange);
    document.addEventListener("webkitfullscreenchange", onFullscreenChange);
    document.addEventListener("mozfullscreenchange", onFullscreenChange);
    document.addEventListener("MSFullscreenChange", onFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", onFullscreenChange);
      document.removeEventListener(
        "webkitfullscreenchange",
        onFullscreenChange
      );
      document.removeEventListener("mozfullscreenchange", onFullscreenChange);
      document.removeEventListener("MSFullscreenChange", onFullscreenChange);
    };
  }, []);

  useEffect(() => {
    const mediaQueryList = window.matchMedia("(display-mode: fullscreen)");

    const onMediaQueryChange = ({ matches }) => {
      setIsFullscreen((prevState) => {
        if (prevState !== matches) {
          return matches;
        }
        return prevState;
      });
    };

    mediaQueryList.addListener(onMediaQueryChange);

    return () => {
      mediaQueryList.removeListener(onMediaQueryChange);
    };
  }, []);

  const handleFullscreenClick = () => {
    enterFullscreen();
  };

  const handleFullscreen = () => {
    let DataObj = {
      data: {
        usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
        evaluation: currentSection.evaluation,
        assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
        sectionId: parseInt(currentSection.ModuleID),
        questionId: parseInt(localStorage.getItem("Question")),
        fullscreen: isFullscreen,
      },
      requestOptions: {
        authorization: `Bearer ${localStorage.getItem("CP-CANDIDATE-TOKEN")}`,
      },
    };
    const workerInstance = worker();
    workerInstance.postToApi({ data: DataObj });
    workerInstance.addEventListener("message", (e) => {
      const { type, result } = e.data;
      if (type === "POST_RESPONSE") {
        setResponse(result);
        workerInstance.terminate();
      }
    });
  };

  useEffect(() => {
    handleFullscreen();
  }, [isFullscreen]);
  //#endregion

  //#region useffect #8 -> Resetting counter
  useEffect(() => {
    setCounter(0);
  }, [data?.meta?.hasNextPage !== false]);
  //#endregion

  //#region Post question
  const { mutate, isLoading: questionLoading } = useMutation(postQuestion, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("/evaluationUsersResponse");
      localStorage.removeItem("exam");
      currentSection.pageQuestion = currentSection.pageQuestion + 1;
      dispatch(setResumeToFalse(false));
      localStorage.setItem("Current Module", JSON.stringify(currentSection));
      handleLocalStorage(response?.id);
      setSelectedOption([]);
      if (data?.meta?.hasNextPage === false) {
        handleFullscreenClick();
        dispatch(setResumeToFalse(false));
        dispatch(setQuestionNumberResume(0));
        navigate("/module-feedback");
      }
    },
    onError: (error) => {},
  });

  const { mutate: updateMutate, isLoading: updateLoading } = useMutation(
    updateQuestion,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/evaluationUsersResponse");
        localStorage.removeItem("exam");
        currentSection.pageQuestion = currentSection.pageQuestion + 1;
        localStorage.setItem("Current Module", JSON.stringify(currentSection));
        dispatch(setResumeToFalse(false));
        handleLocalStorage(response?.id);
        setSelectedOption([]);
        if (data?.meta?.hasNextPage === false) {
          handleFullscreenClick();
          dispatch(setResumeToFalse(false));
          dispatch(setQuestionNumberResume(0));
          navigate("/module-feedback");
        }
      },
    }
  );

  const { mutate: submitMutate, isLoading: mutatesubmitloading } = useMutation(
    postQuestion,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/evaluationUsersResponse");
        localStorage.removeItem("exam");
        dispatch(setExamtoTrue(true));
        dispatch(setResumeToFalse(false));
        dispatch(setQuestionNumberResume(0));
        navigate("/module-feedback");
      },
    }
  );

  const { mutate: updateSubmit, isLoading: updateSubmitLoading } = useMutation(
    updateQuestion,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/evaluationUsersResponse");
        localStorage.removeItem("exam");
        dispatch(setExamtoTrue(true));
        dispatch(setResumeToFalse(false));
        dispatch(setQuestionNumberResume(0));
        navigate("/module-feedback");
      },
    }
  );

  //#endregion

  //#region Posting Question 1 by 1
  const handlePostQuestion = () => {
    // Common data structure for both cases
    let commonData = {
      usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
      assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
      sectionId: parseInt(currentSection.ModuleID),
      questionId: questionID,
      evaluation: currentSection.evaluation,
      selectedOptions: selectedOption,
      status: "abc",
    };

    if (data?.data[0]?.type === "excel") {
      commonData.excelResponse = excelApidata?.excelData;
    }

    // Check if extra parameters need to be added
    if (data?.meta?.hasNextPage === false || totalDuration === 0) {
      commonData.timeTakenToAnswer = barWidth;
      commonData.isSectionLastQuestion = true;
    }

    // Prepare the data for the first API call
    let data_new = JSON.stringify(commonData);

    // Prepare the data for the second API call
    let data2 = {
      content: { ...commonData },
      id: res_id,
    };

    try {
      if (!questState) {
        mutate(data_new);
      } else {
        updateMutate(data2);
      }
    } catch (err) {
      console.error("Error occurred while submitting:", err); // Error handling
    }
  };

  //#endregion

  //#region Handle ID
  const handleRadios = (id) => {
    setSelectedOption((prevSelected) =>
      prevSelected.includes(id) ? [] : [id]
    );
  };

  //#endregion

  //#region Handle checboxes
  const handleCheckbox = (id) => {
    if (selectedOption.includes(id)) {
      setSelectedOption(selectedOption.filter((item) => item !== id));
    } else {
      setSelectedOption([...selectedOption, id]);
    }
  };
  //#endregion

  //#region useffect #9 -> Reset Timer
  useEffect(() => {
    const interval = setInterval(() => {
      setRemaining((prevTime) => {
        return prevTime + 1;
      });
    }, 1000);
    timecheck.seconds = remaining;
    localStorage.setItem("time", JSON.stringify(timecheck));
    return () => clearInterval(interval);
  }, [timecheck]);

  const width = Math.min(
    (timecheck.seconds / (parseInt(currentSection.time) * 60)) * 100,
    100
  );
  timecheck.timeLeft = width;
  localStorage.setItem("time", JSON.stringify(timecheck));
  const remainingTime = parseInt(currentSection.time) * 60;
  //#endregion

  //#region Handling skip
  const handleSkipQuestion = () => {
    localStorage.removeItem("exam");
    let skipData = {
      usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
      assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
      sectionId: parseInt(currentSection.ModuleID),
      questionId: questionID,
      evaluation: currentSection.evaluation,
      selectedOptions: [],
      status: "abc",
    };

    if (data?.data[0]?.type === "excel") {
      skipData.excelResponse = excelApidata?.excelData;
    }

    // Check if extra parameters need to be added
    if (data?.meta?.hasNextPage === false) {
      skipData.timeTakenToAnswer = barWidth;
      skipData.isSectionLastQuestion = true;
    }

    // Prepare the data for the first API call
    let data_new = JSON.stringify(skipData);

    // Prepare the data for the second API call
    let data2 = {
      content: { ...skipData },
      id: res_id,
    };

    try {
      if (!questState) {
        mutate(data_new);
      } else {
        updateMutate(data2);
      }
    } catch (err) {
      console.error("Error occurred while submitting:", err); // Error handling
    }
  };
  //#endregion

  //#region useffect #10 -> If module got no questions
  useEffect(() => {
    if (!isLoading) {
      if (data?.data?.length === 0) {
        dispatch(setResumeToFalse(false));
        dispatch(setQuestionNumberResume(0));
        navigate("/module-feedback");
      }
    }
  }, [data?.data?.length]);
  //#endregion

  //#region useffect #11 -> Handling tab close
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      // Modern browsers
      const message = "Are you sure you want to exit this text?";
      event.preventDefault();
      event.returnValue = message;
      return message;
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        handleTestStop();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);
  //#endregion

  //#region handle submit
  const handleSubmit = () => {
    // Create the common data object
    let dataz = {
      usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
      assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
      sectionId: parseInt(currentSection.ModuleID),
      questionId: questionID,
      evaluation: currentSection.evaluation,
      selectedOptions: selectedOption,
      status: "abc",
    };

    if (data?.data[0]?.type === "excel") {
      dataz.excelResponse = excelApidata?.excelData;
    }

    // Conditionally add extra parameters if the criteria are met
    if (data?.meta?.hasNextPage === false || totalDuration === 0) {
      dataz.isSectionLastQuestion = true;
      dataz.timeTakenToAnswer = barWidth;
    }

    // Convert the data object to a JSON string
    const jsonData = JSON.stringify(dataz);

    let data2 = {
      content: { ...dataz },
      id: res_id,
    };

    try {
      // Submit the data using the mutate function

      if (!questState) {
        submitMutate(jsonData);
      } else {
        updateSubmit(data2);
      }

      // submitMutate(jsonData);
    } catch (err) {
      // Error handling
      console.error("Error occurred while submitting:", err);
    }
  };
  //#endregion

  //#region calculating percentage for number of questions solved / skipped
  question.current = data?.meta?.page - 1;
  localStorage.setItem("questions", JSON.stringify(question));

  question.total = data?.meta?.itemCount;
  localStorage.setItem("questions", JSON.stringify(question));

  question.solved = Math.min((question.current / question.total) * 100);
  localStorage.setItem("questions", JSON.stringify(question));
  //#endregion

  //#region Update Status
  const { mutate: mutateStatus, isLoading: mutateLoading } = useMutation(
    updateStatus,
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/evaluation");
      },
    }
  );

  const handleUpdate = () => {
    let sessionInfo = {
      sectionId: parseInt(currentSection.ModuleID),
      questionId: questionID,
      evaluation: currentSection.evaluation,
      seconds_left: minsec.secleft,
      total_seconds: minsec.secBar,
      currentModuleNumber: currentSection.CurrentModule,
      PracticeSectionModule: practiceSection?.CurrentModule,
      PracticeModuleID: practiceSection?.ModuleID,
      PracticePage: practiceSection?.pageQuestion,
      PracticeTime: practiceSection?.time,
      timeWidth: timecheck.timeLeft,
      CompletionTime: timecheck.seconds,
      QuestionPage: currentSection?.pageQuestion,
      totalModuleTime: currentSection?.time,
      currentModule: moduleData?.current,
      TotalModules: moduleData?.last,
      time_consumed: barWidth,
      lastModule:
        parseInt(moduleData?.current) === parseInt(moduleData?.last)
          ? true
          : false,
    };
    let datanew = {
      evaluation: currentSection.evaluation,
      data: JSON.stringify({
        user: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
        assessment: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
        status: "InProgress",
        sessionInfo: JSON.stringify(sessionInfo),
      }),
    };
    try {
      mutateStatus(datanew);
    } catch (err) {
      //catching error
    }
  };

  const handleUpdateComplete = () => {
    let datanew = {
      evaluation: currentSection.evaluation,
      data: JSON.stringify({
        // end: handleConvert(barWidth),
        end: currentTimeUnix,
        status: "completed",
      }),
    };
    try {
      mutateStatus(datanew);
    } catch (err) {
      //catching error
    }
  };

  const [totalbarWidth, setTotalBarWidth] = useState(minsec.secBar);
  const [barWidth, setBarWidth] = useState(timecheck?.time_consumed);

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setTotalDuration((prevDuration) => {
        if (prevDuration > 0) {
          return prevDuration - 1;
        } else {
          clearInterval(timerInterval);
          return prevDuration;
        }
      });
    }, 1000);
    minsec.secleft = totalDuration;
    localStorage.setItem("minsec", JSON.stringify(minsec));
    return () => clearInterval(timerInterval);
  }, [totalDuration]);

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setBarWidth((prevDuration) => {
        if (barWidth !== totalbarWidth) {
          return prevDuration + 1;
        } else {
          clearInterval(timerInterval);
          return prevDuration;
        }
      });
    }, 1000);
    timecheck.time_consumed = barWidth;
    localStorage.setItem("time", JSON.stringify(timecheck));
    return () => clearInterval(timerInterval);
  }, [barWidth]);

  const minutes = Math.floor(totalDuration / 60);
  const seconds = totalDuration % 60;

  const formattedTime = `${String(minutes).padStart(2, "0")}:${String(
    seconds
  ).padStart(2, "0")}`;

  useEffect(() => {
    {
      if (totalDuration === 0 && moduleData.current !== moduleData.last) {
        handlePostQuestion();
        dispatch(setResumeToFalse(false));
        dispatch(setQuestionNumberResume(0));
        navigate("/module-feedback");
      } else if (
        totalDuration === 0 &&
        moduleData.current === moduleData.last
      ) {
        localStorage.setItem("exam", "start");
        handlePostQuestion();
        handleUpdateComplete();
        dispatch(setExamtoTrue(true));
        setTimeout(() => {
          dispatch(setResumeToFalse(false));
          dispatch(setQuestionNumberResume(0));
          navigate("/module-feedback");
        }, 500);
      }
    }
  }, [
    totalDuration,
    parseInt(moduleData.current, 10),
    parseInt(moduleData.last, 10),
  ]);
  //#endregion

  //#region handle back browser
  useEffect(() => {
    window.history.pushState(null, null, window.location.href);
    window.onpopstate = function (event) {
      window.history.pushState(null, null, window.location.href);
    };
    return () => {
      window.onpopstate = null;
    };
  }, []);
  //#endregion

  //#region montinor browser cam settings
  const checkCameraPermission = async () => {
    try {
      const permissionStatus = await navigator.permissions.query({
        name: "camera",
      });

      if (permissionStatus.state === "denied") {
        setAccessDenied(true);
        setCamLogs(false);
      } else if (permissionStatus.state === "prompt") {
        setAccessDenied(false);
        setCamLogs(false);
      } else if (permissionStatus.state === "granted") {
        try {
          // Try to access the camera stream
          const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
          });
          setAccessDenied(false);
          setCamLogs(true);

          // Stop all tracks to release the camera
          stream.getTracks().forEach((track) => track.stop());
        } catch (error) {
          console.error("Error accessing the camera stream:", error);
          setAccessDenied(false);
          setCamLogs(false);
        }
      }
    } catch (error) {
      console.error("Error checking camera permission:", error);
    }
  };

  useEffect(() => {
    checkCameraPermission();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      checkCameraPermission();
    }, 2000);

    return () => clearInterval(interval);
  }, [accessDenied, camLogs]);

  //#endregion

  //#region Adjusting colors
  const [isHovered, setIsHovered] = useState(false);

  function darkenHexColor(hex, percent) {
    // Ensure the hex code is in the correct format
    hex = hex?.replace(/^#/, "");

    // Convert 3-digit hex to 6-digit hex
    if (hex?.length === 3) {
      hex = hex
        .split("")
        .map((char) => char + char)
        .join("");
    }

    // Convert hex to RGB
    let r = parseInt(hex?.substring(0, 2), 16);
    let g = parseInt(hex?.substring(2, 4), 16);
    let b = parseInt(hex?.substring(4, 6), 16);

    // Calculate the darkened color
    r = Math.floor(r * (1 - percent / 100));
    g = Math.floor(g * (1 - percent / 100));
    b = Math.floor(b * (1 - percent / 100));

    // Convert RGB back to hex
    const darkenedHex = `#${r.toString(16).padStart(2, "0")}${g
      .toString(16)
      .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;

    return darkenedHex;
  }

  // Example usage:
  const originalColor = theme.color;
  const darkenedColor = darkenHexColor(originalColor, 15);

  const buttonStyle = {
    background: isHovered ? darkenedColor : theme.color,
    transition: "background-color 0.1s, transform 0.1s",
    transform: isHovered ? "scale(1.02)" : "scale(1)",
    color: theme.sec_color,
    border: `1px solid ${theme.sec_color}`,
    fontFamily: "Archia Semibold",
  };

  const buttonStyle3 = {
    background: isHovered ? `rgba(${theme.sec_color}, 1.9)` : theme.sec_color,
    transition: "background-color 0.3s, transform 0.6s",
    transform: isHovered ? "scale(1.03)" : "scale(1)",
    fontFamily: "Archia Semibold",
    color: theme.color,
    border: `1px solid ${theme.color}`,
  };

  const handleHover = () => {
    setIsHovered(true);
  };

  const handleLeave = () => {
    setIsHovered(false);
  };

  function DarkenColor(col, amt) {
    if (col === "#000000") {
      // If the color is black, handle it differently
      return "#454545";
    }

    var usePound = false;

    if (col[0] === "#") {
      col = col.slice(1);
      usePound = true;
    }

    var num = parseInt(col, 16);

    var r = (num >> 16) - amt;

    if (r < 0) r = 0;

    var b = ((num >> 8) & 0x00ff) - amt;

    if (b < 0) b = 0;

    var g = (num & 0x0000ff) - amt;

    if (g < 0) g = 0;

    return (
      (usePound ? "#" : "") +
      ("000000" + (g | (b << 8) | (r << 16)).toString(16)).slice(-6)
    );
  }

  var DarkenedColor = DarkenColor(theme.color, 50);

  //#endregion

  //#region taking screenshots
  useEffect(() => {
    // Start capturing screenshots
    const interval = setInterval(() => takeScreenshot(), 2000); // 5 seconds
    setScreenshotInterval(interval);

    // Clean up on component unmount
    return () => clearInterval(interval);
  }, []);

  const takeScreenshot = () => {
    // Create video element to show camera stream
    const video = document.createElement("video");
    video.setAttribute("playsinline", ""); // Required for iOS
    video.setAttribute("autoplay", "");
    video.setAttribute("muted", "");
    video.style.width = "100%";
    video.style.height = "100%";

    // Create canvas for capturing the image
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    // Get user media with specific constraints for better iOS compatibility
    const constraints = {
      video: {
        facingMode: "user",
        width: { ideal: 1280 },
        height: { ideal: 720 },
      },
    };

    return navigator.mediaDevices
      .getUserMedia(constraints)
      .then((stream) => {
        video.srcObject = stream;

        return new Promise((resolve) => {
          video.onloadedmetadata = () => {
            video.play();
            // Set canvas size to match video dimensions
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Wait a bit for the video to start playing
            setTimeout(() => {
              // Draw video frame to canvas
              ctx.drawImage(video, 0, 0);

              // Stop all tracks
              stream.getTracks().forEach((track) => track.stop());

              // Convert to blob and upload
              canvas.toBlob(
                (blob) => {
                  resolve(blob);
                  UploadImageToApi(blob);
                },
                "image/jpeg",
                0.95
              );
            }, 300); // Added delay to ensure video is playing
          };
        });
      })
      .catch((error) => {
        // console.error("Error accessing camera:", error);
        // throw error;
      });
  };
  //#endregion

  //#region check if user completed feedback
  useEffect(() => {
    if (completion_check === "yes") {
      dispatch(setResumeToFalse(false));
      navigate("/feedback");
    } else if (user_exists === "yes") {
      dispatch(setResumeToFalse(false));
      navigate("/candidate/dashboard");
    } else if (user_exists === "no") {
      dispatch(setResumeToFalse(false));
      navigate("/candidate/login");
    }
  }, [completion_check, user_exists]);
  //#endregion

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState(null);

  const handleImageClick = (imageUrl) => {
    setSelectedImageUrl(imageUrl);
    setIsModalOpen(true);
  };

  useEffect(() => {
    if (resumeTest && parseInt(resumeTestQuestion) === 0) {
      const questionNumber = question.current
        ? parseInt(question.current, 10)
        : 0;
      if (!isNaN(questionNumber)) {
        dispatch(setQuestionNumberResume(questionNumber));
      }
    }
  }, [resumeTest, resumeTestQuestion, question, dispatch]);

  //#region Function to stop test
  const handleTestStop = () => {
    let data = JSON.stringify({
      usersId: parseInt(localStorage.getItem("CP-CANDIDATE-ID")),
      assessmentId: parseInt(localStorage.getItem("CANDIDATE-ASSESSMENT-ID")),
    });
    try {
      stopMutate(data);
    } catch (err) {
      console.log(err.message);
    }
  };

  const { mutate: stopMutate, isLoading: stopLoading } = useMutation(stopTest, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("/evaluation/log-stopped");
      localStorage.setItem("stop_status", "true");
    },
  });
  //#endregion

  useEffect(() => {
    if (showSheetModal) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [showSheetModal]);

  document.title = "Exam | Dexta";

  return (
    <>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        enableMultiContainer={false}
        limit={1}
      />
      <nav className="w-full top-0 left-0 py-2 bg-white">
        <div className="w-3/4 px-2 items-center py-2 text-xl text-coalColor mx-auto">
          <div className="grid md:grid-cols-3 container mx-auto">
            <img
              src={theme.logo}
              className="my-auto object-contain sm:max-lg:w-[100px] sm:max-lg:h-[70px]"
              style={{ height: isMobile ? "50px" : "70px" }}
            />
            <div className="my-auto w-full mt-3 md:mt-auto">
              <div className="flex flex-col gap-2">
                <div className="flex flex-row h-5">
                  <div className="w-20 flex flex-row">
                    <BsFillClockFill
                      color={DarkenedColor}
                      className="w-4 h-4 my-auto"
                    />
                    <p
                      className="text-coalColor ml-2 text-sm flex items-center"
                      style={{ fontFamily: "Silka" }}
                    >
                      {isLoading ? (
                        <span className="flex items-center justify-center ml-2 font-bold">
                          <div
                            className="inline-block h-5 w-5 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
                            role="status"
                          >
                            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                              Loading...
                            </span>
                          </div>
                        </span>
                      ) : (
                        <>{formattedTime}</>
                      )}
                    </p>
                  </div>
                  <div
                    className="w-full rounded-lg ml-2 my-auto"
                    style={{ background: theme?.sec_color }}
                  >
                    <div
                      className="p-2 rounded-lg text-center text-xs font-medium leading-none text-primary-100"
                      style={{
                        width: `${(barWidth / totalbarWidth) * 100}%`,
                        background: DarkenedColor,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="flex flex-row h-5">
                  <div className="w-20 flex flex-row">
                    <FaRunning
                      color={DarkenedColor}
                      className="w-4 h-4 my-auto"
                    />
                    <p
                      className="text-coalColor ml-2 text-sm flex items-center"
                      style={{ fontFamily: "Silka" }}
                    >
                      {isLoading ? (
                        <span className="flex items-center justify-center ml-2 font-bold">
                          <div
                            className="inline-block h-5 w-5 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
                            role="status"
                          >
                            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                              Loading...
                            </span>
                          </div>
                        </span>
                      ) : (
                        <>
                          {question.current + 1}/{question.total}
                        </>
                      )}
                    </p>
                  </div>
                  <div
                    className="w-full rounded-lg ml-2 my-auto"
                    style={{ background: theme?.sec_color }}
                  >
                    <div
                      className="p-0.5 rounded-lg py-2 text-center text-xs font-medium leading-none text-primary-100"
                      style={{
                        width: `${question.solved}%`,
                        background: DarkenedColor,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="my-auto ml-auto flex sm:mt-3 md:mt-auto">
              <div>
                {moduleData.current === moduleData.last &&
                data?.meta?.hasNextPage === false ? (
                  <div className="flex flex-row gap-3">
                    {/* Back button */}
                    <div className="relative flex">
                      <button
                        className={`inline-flex items-center justify-center  py-3 my-auto ${
                          data?.meta?.hasPreviousPage === false &&
                          "cursor-not-allowed"
                        } text-xs 2xl:text-sm font-medium rounded-md`}
                        style={{
                          backgroundColor:
                            data?.meta?.hasPreviousPage === false ||
                            parseInt(question.current) <=
                              parseInt(resumeTestQuestion)
                              ? "#D3D5D8"
                              : theme?.sec_color,
                          fontFamily: "Archia Semibold",
                          color:
                            data?.meta?.hasPreviousPage === false ||
                            parseInt(question.current) <=
                              parseInt(resumeTestQuestion)
                              ? "#7C8289"
                              : theme.color,
                          width: isMobile ? "110px" : "130px",
                        }}
                        disabled={
                          data?.meta?.hasPreviousPage === false ||
                          questionLoading ||
                          isLoading ||
                          backLoading ||
                          parseInt(question.current) <=
                            parseInt(resumeTestQuestion)
                        }
                        onClick={() => {
                          handleBackQuestion();
                        }}
                      >
                        <AiOutlineArrowLeft
                          color={DarkenColor}
                          className="w-5 h-5 mr-2 icon-image"
                        />
                        {t("test-screen.back")}
                      </button>
                      {parseInt(question.current) <=
                        parseInt(resumeTestQuestion) &&
                        parseInt(resumeTestQuestion) !== 0 && (
                          <div
                            className="tooltip w-[14rem] bg-red-100 text-red-700 p-4 rounded-lg shadow-lg z-10 text-center text-sm font-semibold"
                            style={{ fontFamily: "Silka" }}
                          >
                            <p>{t("test-screen.cannot_go_back")}</p>
                            <span className="block text-xs text-gray-400 mt-2">
                              {t("test-screen.navigation_restricted")}
                            </span>
                          </div>
                        )}
                    </div>

                    <button
                      className={`inline-flex items-center justify-center  py-3 my-auto ${
                        selectedOption.length === 0 && "bg-[#FFFFFF]/90"
                      } text-xs 2xl:text-sm  font-medium rounded-md`}
                      style={{
                        color: theme.color,
                        backgroundColor: theme?.sec_color,
                        fontFamily: "Archia Semibold",
                        width: isMobile ? "110px" : "130px",
                      }}
                      onClick={() => {
                        if (selectedOption.length === 0 && !cellsFilled) {
                          setSkipModal(true);
                        } else {
                          handleSubmit();
                          setCounter(counter + 1);
                          handleApiRequest();
                          setTimeout(() => {
                            handleUpdateComplete();
                          }, 1000);
                        }
                      }}
                    >
                      {selectedOption.length === 0 && !cellsFilled
                        ? t("test-screen.skip")
                        : t("test-screen.submit")}
                      <AiOutlineArrowRight className="w-5 h-5 ml-2 icon-image" />
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-row gap-3">
                    <div className="relative flex">
                      <button
                        className={`inline-flex items-center justify-center  py-3 my-auto ${
                          data?.meta?.hasPreviousPage === false &&
                          "cursor-not-allowed"
                        } text-xs 2xl:text-sm  font-medium rounded-md`}
                        style={{
                          backgroundColor:
                            data?.meta?.hasPreviousPage === false ||
                            parseInt(question.current) <=
                              parseInt(resumeTestQuestion)
                              ? "#D3D5D8"
                              : theme?.sec_color,
                          fontFamily: "Archia Semibold",
                          color:
                            data?.meta?.hasPreviousPage === false ||
                            parseInt(question.current) <=
                              parseInt(resumeTestQuestion)
                              ? "#7C8289"
                              : theme.color,
                          width: isMobile ? "110px" : "130px", // Set a fixed width
                        }}
                        disabled={
                          data?.meta?.hasPreviousPage === false ||
                          questionLoading ||
                          isLoading ||
                          parseInt(question.current) <=
                            parseInt(resumeTestQuestion)
                        }
                        onClick={() => {
                          handleBackQuestion();
                        }}
                      >
                        <AiOutlineArrowLeft
                          color={DarkenColor}
                          className="w-5 h-5 mr-2 icon-image"
                        />
                        {t("test-screen.back")}
                      </button>
                      {parseInt(question.current) <=
                        parseInt(resumeTestQuestion) &&
                        parseInt(resumeTestQuestion) !== 0 && (
                          <div
                            className="tooltip w-[14rem] bg-red-100 text-red-700 p-4 rounded-lg shadow-lg z-10 text-center text-sm font-semibold"
                            style={{ fontFamily: "Silka" }}
                          >
                            <p>{t("test-screen.cannot_go_back")}</p>
                            <span className="block text-xs text-gray-400 mt-2">
                              {t("test-screen.navigation_restricted")}
                            </span>
                          </div>
                        )}
                    </div>
                    {selectedOption.length === 0 && !cellsFilled ? (
                      <button
                        className="inline-flex items-center justify-center  py-3 my-auto hover:bg-[#FFFFFF]/90 text-sm font-medium rounded-md"
                        style={{
                          color: theme.color,
                          backgroundColor: theme?.sec_color,
                          fontFamily: "Archia Semibold",
                          width: isMobile ? "110px" : "130px",
                        }}
                        disabled={questionLoading || isLoading || updateLoading}
                        onClick={() => setSkipModal(true)}
                      >
                        {t("test-screen.skip")}
                        <AiOutlineArrowRight
                          color={DarkenColor}
                          className="w-5 h-5 ml-2 icon-image"
                        />
                      </button>
                    ) : (
                      <button
                        className="inline-flex items-center justify-center py-3 my-auto text-xs 2xl:text-sm  font-medium rounded-md"
                        style={{
                          color: theme.color,
                          backgroundColor: theme?.sec_color,
                          fontFamily: "Archia Semibold",
                          width: isMobile ? "110px" : "130px",
                        }}
                        disabled={questionLoading || isLoading || updateLoading}
                        onClick={() => {
                          handlePostQuestion();
                          handleUpdate();
                          if (data?.meta?.hasNextPage === false) {
                            setCounter(counter + 1);
                            handleApiRequest();
                          }
                        }}
                      >
                        {t("test-screen.continue")}
                        <AiOutlineArrowRight className="w-5 h-5 ml-2 icon-image" />
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>
      <div className="bg-bodyColor w-full">
        <div className="sm:w-5/6 md:w-3/4 px-2 mx-auto items-center">
          <div className=" h-screen lg:container mb-[190px]">
            <div className="xl:mt-[30px] 2xl:mt-[50px] sm:max-md:mt-10 rounded-lg">
              {(!isLoading && !fetchingOptions) ||
                (!backLoading && (
                  <h1
                    className="text-xl font-bold py-5"
                    style={{ fontFamily: "Archia Bold" }}
                  >
                    {/* {currentSection.module_Name} */}
                  </h1>
                ))}
              {isLoading || fetchingOptions || backLoading ? (
                <div class="loader-container-3 col-span-6">
                  <div class="loader-3"></div>
                </div>
              ) : (
                <div className="rounded-md bg-white">
                  <div className="p-3 md:p-8">
                    {data?.data?.map((i, index) => {
                      questionID = i?.id;
                      localStorage.setItem("Question", i?.id);
                      let options_check;
                      if (
                        i?.options?.every((option) => option.imageUrl === null)
                      ) {
                        options_check = "null";
                      } else if (
                        i?.options?.every(
                          (option) =>
                            option.imageUrl !== null && option?.title !== null
                        )
                      ) {
                        options_check = "all";
                      } else if (
                        i?.options?.every(
                          (option) =>
                            option.imageUrl !== null && option?.title === null
                        )
                      ) {
                        options_check = "all images";
                      } else {
                        options_check = "some_image";
                      }
                      return (
                        <React.Fragment key={i.id}>
                          <div className="md:grid md:grid-cols-2 gap-5">
                            <div className="pt-1 2xl:pt-5 md:pl-5 overflow-hidden">
                              <div className="md:pr-8">
                                <h1
                                  className="text-coalColor text-xl"
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {currentSection?.module_Name}
                                </h1>
                                <h1
                                  className="text-coalColor text-lg mt-5 font-bold"
                                  style={{ fontFamily: "Archia Bold" }}
                                >
                                  {t("test-screen.question")}{" "}
                                  {parseInt(question.current) + 1}{" "}
                                  {t("test-screen.of")} {question.total}
                                </h1>
                                <div
                                  className="mt-5"
                                  style={{ fontFamily: "Silka" }}
                                >
                                  <div
                                    className={`${styles["html-content"]} html-content`}
                                  >
                                    {transformDescription(i?.description)}
                                  </div>
                                </div>
                              </div>

                              {i?.image != null &&
                                i?.image != " " &&
                                i?.image != "" && (
                                  <div className="mt-3">
                                    <img
                                      src={i?.image}
                                      className={`rounded-lg cursor-pointer h-auto ${styles.questionImage}`}
                                      style={{
                                        width: "100%",
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleImageClick(i?.image);
                                      }}
                                    />
                                  </div>
                                )}

                              {i?.figImage != null &&
                                i?.figImage != " " &&
                                i?.figImage != "" && (
                                  <div className="">
                                    <img
                                      src={i?.figImage}
                                      className={`rounded-lg cursor-pointer h-auto mt-5 ${styles.questionImage}`}
                                      style={{
                                        width: "100%",
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleImageClick(i?.figImage);
                                      }}
                                    />
                                  </div>
                                )}
                            </div>
                            {i.type === "Single" ? (
                              <>
                                <div className="pt-1 2xl:pt-5 md:pl-2">
                                  <div
                                    className="md:px-10 text-coalColor text-lg font-bold rounded-lg"
                                    style={{
                                      fontFamily: "Archia Bold",
                                      wordWrap: "break-word",
                                      wordBreak: "break-word",
                                    }}
                                  >
                                    {t("test-screen.select_only_one")}
                                    {i?.options
                                      ?.sort((a, b) =>
                                        a?.optionPosition > b?.optionPosition
                                          ? 1
                                          : -1
                                      )
                                      // Group options into pairs for two per row
                                      .reduce((acc, curr, index, array) => {
                                        if (index % 2 === 0)
                                          acc.push(
                                            array.slice(index, index + 2)
                                          );
                                        return acc;
                                      }, [])
                                      .map((pair, rowIndex) => (
                                        <div
                                          key={rowIndex}
                                          className={`grid ${
                                            options_check === "all images" &&
                                            "grid-cols-2"
                                          }  gap-4 mt-5`}
                                        >
                                          {pair.map((j) => (
                                            <>
                                              {options_check ===
                                              "all images" ? (
                                                <div
                                                  key={j.id}
                                                  onClick={() =>
                                                    handleRadios(j.id)
                                                  }
                                                  className={`cursor-pointer hover:animate-[jiggle_1s_ease-in-out_infinite]`}
                                                >
                                                  <div className="relative flex items-center justify-center">
                                                    {/* Container for image and zoom button */}
                                                    <div className="relative group">
                                                      <img
                                                        src={j?.imageUrl}
                                                        className={`w-[270px] h-[200px] rounded-lg transition-opacity duration-300 ${styles.optionImage}`}
                                                        style={{
                                                          borderColor:
                                                            selectedOption.includes(
                                                              j.id
                                                            )
                                                              ? theme.color
                                                              : "white",
                                                          borderStyle: "solid",
                                                          borderWidth: "3px",
                                                        }}
                                                      />

                                                      <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                        <button
                                                          className="bg-white text-black px-3 py-2 rounded-lg shadow-md"
                                                          onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleImageClick(
                                                              j?.imageUrl
                                                            );
                                                          }}
                                                        >
                                                          <img
                                                            src={eye}
                                                            className="w-5 h-5 object-contain"
                                                          />
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              ) : (
                                                <div
                                                  key={j.id}
                                                  onClick={() =>
                                                    handleRadios(j.id)
                                                  }
                                                  className={`p-3 2xl:p-5 mt-5 cursor-pointer ${
                                                    selectedOption.includes(
                                                      j.id
                                                    )
                                                      ? "bg-black"
                                                      : null
                                                  } hover:animate-[jiggle_1s_ease-in-out_infinite] rounded-2xl border border-[#B6B6B6]`}
                                                  style={{
                                                    background:
                                                      selectedOption.includes(
                                                        j.id
                                                      )
                                                        ? theme.color
                                                        : "white",
                                                  }}
                                                >
                                                  <div className="flex justify-between">
                                                    <div className="flex gap-10">
                                                      <div className="inline-flex items-center">
                                                        <label
                                                          className="relative flex cursor-pointer items-center rounded-full p-3"
                                                          data-ripple-dark="true"
                                                        >
                                                          <input
                                                            id={`radio_${j.id}`}
                                                            name="type"
                                                            type="radio"
                                                            className="peer relative 2xl:h-7 2xl:w-7 w-5 h-5 cursor-pointer appearance-none rounded-full border border-blue-gray-200 text-coalColor transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:opacity-0 before:transition-opacity border-black checked:border-coalColor checked:before:bg-black hover:before:opacity-10"
                                                            checked={selectedOption.includes(
                                                              j.id
                                                            )}
                                                          />
                                                          <div className="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-coalColor opacity-0 transition-opacity peer-checked:opacity-100">
                                                            <svg
                                                              xmlns="http://www.w3.org/2000/svg"
                                                              className="2xl:h-5 2xl:w-5 w-3 h-3"
                                                              viewBox="0 0 16 16"
                                                              fill="currentColor"
                                                            >
                                                              <circle
                                                                data-name="ellipse"
                                                                cx="8"
                                                                cy="8"
                                                                r="8"
                                                              ></circle>
                                                            </svg>
                                                          </div>
                                                        </label>
                                                        <label
                                                          className={`mt-px cursor-pointer select-none text-sm font-medium`}
                                                          style={{
                                                            fontFamily: "Silka",
                                                            color:
                                                              selectedOption.includes(
                                                                j.id
                                                              )
                                                                ? theme?.sec_color
                                                                : "#252e3a",
                                                          }}
                                                        >
                                                          {j.title}
                                                        </label>
                                                      </div>
                                                    </div>
                                                    {(options_check === "all" ||
                                                      options_check ===
                                                        "some_image") && (
                                                      <div className="my-auto">
                                                        {j?.imageUrl ? (
                                                          <div className="relative flex items-center justify-center">
                                                            <div className="relative group">
                                                              <img
                                                                src={
                                                                  j?.imageUrl
                                                                }
                                                                className={`my-auto w-20 h-20 ${styles.optionImage}`}
                                                              />
                                                              <div className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                                <button
                                                                  className="bg-white text-black px-2 py-2 rounded-md shadow-md"
                                                                  onClick={(
                                                                    e
                                                                  ) => {
                                                                    e.stopPropagation();
                                                                    handleImageClick(
                                                                      j?.imageUrl
                                                                    );
                                                                  }}
                                                                >
                                                                  <img
                                                                    src={eye}
                                                                    className="w-3 h-3 object-contain"
                                                                  />
                                                                </button>
                                                              </div>
                                                            </div>
                                                          </div>
                                                        ) : (
                                                          <div className="w-20 h-20"></div>
                                                        )}
                                                      </div>
                                                    )}
                                                  </div>
                                                </div>
                                              )}
                                            </>
                                          ))}
                                        </div>
                                      ))}
                                  </div>
                                  <div className="flex justify-center my-auto md:px-10 mt-8">
                                    {moduleData.current === moduleData.last &&
                                    data?.meta?.hasNextPage === false ? (
                                      <button
                                        className={`inline-flex items-center w-full justify-center px-8 py-4 my-auto  text-white text-sm font-medium rounded-md`}
                                        style={buttonStyle}
                                        onMouseEnter={handleHover}
                                        onMouseLeave={handleLeave}
                                        onClick={() => {
                                          if (selectedOption.length === 0) {
                                            setSkipModal(true);
                                          } else {
                                            handleSubmit();
                                            setCounter(counter + 1);
                                            handleApiRequest();
                                            setTimeout(() => {
                                              handleUpdateComplete();
                                            }, 1000);
                                          }
                                        }}
                                      >
                                        {selectedOption.length === 0
                                          ? t("test-screen.skip")
                                          : t("test-screen.submit")}
                                        <GoArrowRight
                                          alt="Add Transaction Icon"
                                          className="w-5 h-5 ml-2 icon-image"
                                        />
                                      </button>
                                    ) : (
                                      <>
                                        {selectedOption.length === 0 ? (
                                          <button
                                            className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-lg"
                                            disabled={
                                              questionLoading ||
                                              isLoading ||
                                              updateLoading
                                            }
                                            style={buttonStyle}
                                            onMouseEnter={handleHover}
                                            onMouseLeave={handleLeave}
                                            onClick={() => setSkipModal(true)}
                                          >
                                            {t("test-screen.skip")}
                                            <GoArrowRight
                                              alt="Add Transaction Icon"
                                              className="w-5 h-5 ml-2 icon-image"
                                            />
                                          </button>
                                        ) : (
                                          <button
                                            className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-md"
                                            style={buttonStyle}
                                            onMouseEnter={handleHover}
                                            onMouseLeave={handleLeave}
                                            onClick={() => {
                                              handleUpdate();
                                              handlePostQuestion();
                                              if (
                                                data?.meta?.hasNextPage ===
                                                false
                                              ) {
                                                setCounter(counter + 1);
                                                handleApiRequest();
                                              }
                                            }}
                                          >
                                            {t("test-screen.continue")}
                                            <GoArrowRight
                                              alt="Add Transaction Icon"
                                              className="w-5 h-5 ml-2 icon-image"
                                            />
                                          </button>
                                        )}
                                      </>
                                    )}
                                  </div>
                                  {isModalOpen && (
                                    <div
                                      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                                      onClick={() => setIsModalOpen(false)}
                                    >
                                      <div className="relative bg-white p-0 lg:p-5 rounded-md">
                                        <img
                                          src={closeIcon}
                                          className="absolute top-3 right-5 z-20 w-6 h-6 cursor-pointer"
                                          onClick={() => setIsModalOpen(false)}
                                        />
                                        <img
                                          src={selectedImageUrl}
                                          alt="Selected"
                                          className={`p-0 lg:p-5 ${styles.modalImage}`}
                                          style={{
                                            height: !isMobile && "450px",
                                            width: "850px",
                                          }}
                                        />
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </>
                            ) : (
                              <>
                                {i.type === "excel" ? (
                                  <div className="pt-1 2xl:pt-5">
                                    <div
                                      className="md:px-10 text-coalColor text-xl font-bold rounded-lg"
                                      style={{
                                        fontFamily: "Archia Bold",
                                        wordWrap: "break-word",
                                        wordBreak: "break-word",
                                      }}
                                    >
                                      <div className="flex justify-between">
                                        <p>
                                          {t("test-screen.insert_answer_excel")}
                                        </p>
                                        <GiExpand
                                          className="my-auto cursor-pointer"
                                          onClick={() =>
                                            setShowSheetModal(true)
                                          }
                                        />
                                      </div>

                                      <div className="mt-5">
                                        <div
                                          className={`${
                                            showSheetModal
                                              ? "fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-75"
                                              : ""
                                          }`}
                                          onClick={() =>
                                            showSheetModal &&
                                            setShowSheetModal(false)
                                          }
                                        >
                                          {/* h-5/6 */}
                                          <div
                                            className={`relative bg-white ${
                                              showSheetModal
                                                ? "p-5 w-5/6  rounded-lg overflow-auto "
                                                : ""
                                            }`}
                                            onClick={(e) => e.stopPropagation()}
                                          >
                                            {showSheetModal && (
                                              <img
                                                src={closeIcon}
                                                alt="Close"
                                                className="absolute top-4 z-50 right-4 w-6 h-6 cursor-pointer"
                                                onClick={() =>
                                                  setShowSheetModal(false)
                                                }
                                              />
                                            )}

                                            {/* ✅ Single ExcelSheets component */}
                                            <ExcelSheets
                                              excelID="app"
                                              cellsData={i?.excelData}
                                              apiData={i}
                                              maskedCells={i?.maskedCells}
                                              cellsFilled={cellsFilled}
                                              SetExcelApiData={SetExcelApiData}
                                              SetCellsFilled={SetCellsFilled}
                                              responseSubmitted={
                                                i?.responseSubmitted
                                                  ?.excelResponse
                                              }
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex justify-center my-auto md:px-10 mt-8">
                                      {moduleData.current === moduleData.last &&
                                      data?.meta?.hasNextPage === false ? (
                                        <button
                                          className={`inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-md`}
                                          style={buttonStyle}
                                          onMouseEnter={handleHover}
                                          onMouseLeave={handleLeave}
                                          onClick={() => {
                                            if (
                                              selectedOption.length === 0 &&
                                              !cellsFilled
                                            ) {
                                              setSkipModal(true);
                                            } else {
                                              handleSubmit();
                                              setCounter(counter + 1);
                                              handleApiRequest();
                                              setTimeout(() => {
                                                handleUpdateComplete();
                                              }, 1000);
                                            }
                                          }}
                                        >
                                          {selectedOption.length === 0 &&
                                          !cellsFilled
                                            ? t("test-screen.skip")
                                            : t("test-screen.submit")}
                                          <GoArrowRight className="w-5 h-5 ml-2 icon-image" />
                                        </button>
                                      ) : (
                                        <>
                                          {selectedOption.length === 0 &&
                                          !cellsFilled ? (
                                            <button
                                              className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-lg"
                                              disabled={
                                                questionLoading ||
                                                isLoading ||
                                                updateLoading
                                              }
                                              style={buttonStyle}
                                              onMouseEnter={handleHover}
                                              onMouseLeave={handleLeave}
                                              onClick={() => setSkipModal(true)}
                                            >
                                              {t("test-screen.skip")}
                                              <GoArrowRight className="w-5 h-5 ml-2 icon-image" />
                                            </button>
                                          ) : (
                                            <button
                                              className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-md"
                                              style={buttonStyle}
                                              onMouseEnter={handleHover}
                                              onMouseLeave={handleLeave}
                                              onClick={() => {
                                                handleUpdate();
                                                handlePostQuestion();
                                                if (
                                                  data?.meta?.hasNextPage ===
                                                  false
                                                ) {
                                                  setCounter(counter + 1);
                                                  handleApiRequest();
                                                }
                                              }}
                                            >
                                              {t("test-screen.continue")}
                                              <GoArrowRight className="w-5 h-5 ml-2 icon-image" />
                                            </button>
                                          )}
                                        </>
                                      )}
                                    </div>
                                  </div>
                                ) : (
                                  <div className="pt-1 2xl:pt-5 md:pl-2">
                                    <div
                                      className="md:px-10 text-coalColor text-xl font-bold rounded-lg"
                                      style={{
                                        fontFamily: "Archia Bold",
                                        wordWrap: "break-word",
                                        wordBreak: "break-word",
                                      }}
                                    >
                                      {t("test-screen.select_all_that_apply")}
                                      {i?.options
                                        ?.sort((a, b) =>
                                          a?.optionPosition > b?.optionPosition
                                            ? 1
                                            : -1
                                        )
                                        .reduce((acc, curr, index, array) => {
                                          if (index % 2 === 0)
                                            acc.push(
                                              array.slice(index, index + 2)
                                            );
                                          return acc;
                                        }, [])
                                        .map((pair, rowIndex) => (
                                          <div
                                            key={rowIndex}
                                            className={`grid ${
                                              options_check === "all images" &&
                                              "grid-cols-2"
                                            }  gap-4 mt-5`}
                                          >
                                            {pair.map((j) => (
                                              <>
                                                {options_check ===
                                                "all images" ? (
                                                  <div
                                                    key={j.id}
                                                    onClick={() =>
                                                      handleCheckbox(j.id)
                                                    }
                                                    className={`cursor-pointer hover:animate-[jiggle_1s_ease-in-out_infinite]`}
                                                  >
                                                    <div className="relative flex items-center justify-center">
                                                      {/* Container for image and zoom button */}
                                                      <div className="relative group">
                                                        <img
                                                          src={j?.imageUrl}
                                                          className={`w-[270px] h-[200px] rounded-lg transition-opacity duration-300 ${styles.optionImage}`}
                                                          style={{
                                                            borderColor:
                                                              selectedOption.includes(
                                                                j.id
                                                              )
                                                                ? theme.color
                                                                : "white",
                                                            borderStyle:
                                                              "solid",
                                                            borderWidth: "3px",
                                                          }}
                                                        />

                                                        <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                          <button
                                                            className="bg-white text-black px-3 py-2 rounded-lg shadow-md"
                                                            onClick={(e) => {
                                                              e.stopPropagation();
                                                              handleImageClick(
                                                                j?.imageUrl
                                                              );
                                                            }}
                                                          >
                                                            <img
                                                              src={eye}
                                                              className="w-5 h-5 object-contain"
                                                            />
                                                          </button>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                ) : (
                                                  <div
                                                    className={`p-3 2xl:p-5 mt-3 rounded-2xl border border-[#B6B6B6] cursor-pointer hover:animate-[jiggle_1s_ease-in-out_infinite]`}
                                                    key={j.id}
                                                    style={{
                                                      background:
                                                        selectedOption.includes(
                                                          j.id
                                                        )
                                                          ? theme.color
                                                          : "white",
                                                    }}
                                                    onClick={() =>
                                                      handleCheckbox(j?.id)
                                                    }
                                                  >
                                                    <div className="flex justify-between">
                                                      <div className="inline-flex items-center">
                                                        <label
                                                          className="relative flex cursor-pointer items-center rounded-full p-3"
                                                          htmlFor={`radio_${j.id}`}
                                                          data-ripple-dark="true"
                                                        >
                                                          <input
                                                            id="ripple-on"
                                                            type="checkbox"
                                                            className={`peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 border-gray-600  before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-10`}
                                                            onClick={() =>
                                                              handleCheckbox(
                                                                j?.id
                                                              )
                                                            }
                                                            checked={selectedOption.includes(
                                                              j.id
                                                            )}
                                                            onChange={() =>
                                                              handleCheckbox(
                                                                j?.id
                                                              )
                                                            }
                                                          />
                                                          <div className="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-coalColor opacity-0 transition-opacity peer-checked:opacity-100">
                                                            <svg
                                                              xmlns="http://www.w3.org/2000/svg"
                                                              className="h-3.5 w-3.5"
                                                              viewBox="0 0 20 20"
                                                              fill="currentColor"
                                                              stroke="currentColor"
                                                              strokeWidth="1"
                                                            >
                                                              <path
                                                                fillRule="evenodd"
                                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                clipRule="evenodd"
                                                              ></path>
                                                            </svg>
                                                          </div>
                                                        </label>
                                                        <label
                                                          className={`mt-px cursor-pointer select-none text-sm font-medium ${
                                                            selectedOption?.includes(
                                                              j?.id
                                                            )
                                                              ? "text-[#FFFFFF]"
                                                              : "text-coalColor"
                                                          }`}
                                                          htmlFor="html"
                                                          style={{
                                                            fontFamily: "Silka",
                                                            color:
                                                              selectedOption.includes(
                                                                j.id
                                                              )
                                                                ? theme?.sec_color
                                                                : "#252e3a",
                                                          }}
                                                        >
                                                          {j.title}
                                                        </label>
                                                      </div>
                                                      {(options_check ===
                                                        "all" ||
                                                        options_check ===
                                                          "some_image") && (
                                                        <div className="my-auto">
                                                          {j?.imageUrl ? (
                                                            <div className="relative flex items-center justify-center">
                                                              {/* Container for image and zoom button */}
                                                              <div className="relative group">
                                                                <img
                                                                  src={
                                                                    j?.imageUrl
                                                                  }
                                                                  className={`my-auto w-20 h-20 ${styles.optionImage}`}
                                                                />

                                                                <div className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                                  <button
                                                                    className="bg-white text-black px-2 py-2 rounded-md shadow-md"
                                                                    onClick={(
                                                                      e
                                                                    ) => {
                                                                      e.stopPropagation();
                                                                      handleImageClick(
                                                                        j?.imageUrl
                                                                      );
                                                                    }}
                                                                  >
                                                                    <img
                                                                      src={eye}
                                                                      className="w-3 h-3 object-contain"
                                                                    />
                                                                  </button>
                                                                </div>
                                                              </div>
                                                            </div>
                                                          ) : (
                                                            <div className="w-20 h-20"></div>
                                                          )}
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                )}
                                              </>
                                            ))}
                                          </div>
                                        ))}
                                    </div>

                                    <div className="flex justify-center my-auto md:px-10 mt-8">
                                      {moduleData.current === moduleData.last &&
                                      data?.meta?.hasNextPage === false ? (
                                        <button
                                          className={`inline-flex items-center w-full justify-center px-8 py-4 my-auto  ${
                                            selectedOption.length === 0
                                              ? "cursor-not-allowed"
                                              : ""
                                          }  text-white text-sm font-medium rounded-md`}
                                          disabled={selectedOption.length === 0}
                                          style={buttonStyle}
                                          onMouseEnter={handleHover}
                                          onMouseLeave={handleLeave}
                                          onClick={() => {
                                            handleSubmit();
                                            setCounter(counter + 1);
                                            handleApiRequest();
                                            setTimeout(() => {
                                              handleUpdateComplete();
                                            }, 1000);
                                          }}
                                        >
                                          {t("test-screen.submit")}
                                          <GoArrowRight
                                            alt="Add Transaction Icon"
                                            className="w-5 h-5 ml-2 icon-image"
                                          />
                                        </button>
                                      ) : (
                                        <>
                                          {selectedOption.length === 0 ? (
                                            <button
                                              className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-lg"
                                              disabled={
                                                questionLoading ||
                                                isLoading ||
                                                updateLoading
                                              }
                                              style={buttonStyle}
                                              onMouseEnter={handleHover}
                                              onMouseLeave={handleLeave}
                                              onClick={() => setSkipModal(true)}
                                            >
                                              {t("test-screen.skip")}
                                              <GoArrowRight
                                                alt="Add Transaction Icon"
                                                className="w-5 h-5 ml-2 icon-image"
                                              />
                                            </button>
                                          ) : (
                                            <button
                                              className="inline-flex items-center w-full justify-center px-8 py-4 my-auto text-white text-sm font-medium rounded-md"
                                              style={buttonStyle}
                                              onMouseEnter={handleHover}
                                              onMouseLeave={handleLeave}
                                              onClick={() => {
                                                handleUpdate();
                                                handlePostQuestion();
                                                if (
                                                  data?.meta?.hasNextPage ===
                                                  false
                                                ) {
                                                  setCounter(counter + 1);
                                                  handleApiRequest();
                                                }
                                              }}
                                            >
                                              {t("test-screen.continue")}
                                              <GoArrowRight
                                                alt="Add Transaction Icon"
                                                className="w-5 h-5 ml-2 icon-image"
                                              />
                                            </button>
                                          )}
                                        </>
                                      )}
                                    </div>
                                    {isModalOpen && (
                                      <div
                                        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                                        onClick={() => setIsModalOpen(false)}
                                      >
                                        <div className="relative bg-white p-5 rounded-md">
                                          <img
                                            src={closeIcon}
                                            className="absolute top-3 right-5 z-20 w-6 h-6 cursor-pointer"
                                            onClick={() =>
                                              setIsModalOpen(false)
                                            }
                                          />
                                          <img
                                            src={selectedImageUrl}
                                            alt="Selected"
                                            className={`p-5 ${styles.modalImage}`}
                                            style={{
                                              height: "450px",
                                              width: "850px",
                                            }}
                                          />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {skipModal && (
        <div
          id="crypto-modal"
          tabindex="-1"
          aria-hidden="true"
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-60"
          style={{ zIndex: 999 }}
        >
          <div class="relative p-4 w-full max-w-xl max-h-full">
            <div class="relative bg-white rounded-lg shadow ">
              <button
                type="button"
                class="absolute end-1 text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center  hover:bg-gray-200"
                data-modal-hide="popup-modal"
                onClick={() => setSkipModal(false)}
              >
                <img src={closeImg} className="w-5 h-5" />

                <span class="sr-only">Close modal</span>
              </button>
              <div class="px-6 py-4 pl-2 text-center">
                <h3
                  class="mb-5 text-xl font-bold text-gray-800 "
                  style={{ fontFamily: "Archia Bold" }}
                >
                  {t("test-screen.skip_question_modal_title")}
                </h3>
                <p className="px-4 mt-8" style={{ fontFamily: "Silka" }}>
                  {t("test-screen.skip_question_modal_desc")}
                </p>
                <div
                  className="flex flex-row justify-end mt-8 gap-3"
                  style={{ fontFamily: "Silka" }}
                >
                  <button
                    className="inline-flex items-center justify-center px-4 py-2.5 my-auto text-white border border-black text-sm font-medium rounded-lg bg-coalColor"
                    disabled={questionLoading || isLoading}
                    style={buttonStyle3}
                    onClick={() => {
                      if (
                        moduleData.current === moduleData.last &&
                        data?.meta?.hasNextPage === false
                      ) {
                        handleSubmit();
                        setCounter(counter + 1);
                        handleApiRequest();
                        setTimeout(() => {
                          handleUpdateComplete();
                        }, 1000);
                        setSkipModal(false);
                      } else {
                        handleUpdate();
                        handleSkipQuestion();
                        setSkipModal(false);
                      }
                    }}
                  >
                    {!questionLoading
                      ? t("test-screen.skip_question")
                      : t("test-screen.skipping")}
                  </button>
                  <button
                    className="inline-flex items-center justify-center px-4 py-3 bg-primaryGreen my-auto text-coalColor border border-coalColor text-sm font-medium rounded-lg"
                    disabled={questionLoading || isLoading}
                    onClick={() => setSkipModal(false)}
                    style={buttonStyle}
                  >
                    {t("test-screen.choose_answer")}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Test;
